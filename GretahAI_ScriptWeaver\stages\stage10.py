"""
Stage 10: Script Playground for GretahAI ScriptWeaver

This module provides an independent script experimentation and generation utility.
It provides functionality for:
- Loading optimized scripts as templates
- Selecting target test cases for generation
- Template-based AI script generation using proven patterns
- Managing generated scripts with proper metadata
- Independent access regardless of workflow stage

Key Features:
- **Always Accessible**: Can be accessed at any time, regardless of workflow stage
- **Template-Based Generation**: Uses optimized scripts as templates for new test cases
- **AI-Powered Adaptation**: Leverages Google AI to adapt templates to new requirements
- **Quality Preservation**: Maintains optimization patterns and best practices from templates
- **Playground Nature**: Functions as an experimental environment for script generation

The stage maintains the established architectural patterns:
- StateManager pattern with logging and st.rerun() after state changes
- Collapsible sections using st.expander() with expanded=True
- Manual progression with confirmation buttons
- Minimalist UI design with minimal instructional text
- Google AI integration via generate_llm_response

Functions:
    stage10_script_playground(state): Main Stage 10 function for script playground
"""

import os
import logging
import streamlit as st
import tempfile
import subprocess
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional

# Import core dependencies
from state_manager import StateStage
from core.ai import generate_llm_response
from core.ai_helpers import clean_llm_response
from debug_utils import debug

# Import helper functions
from core.template_helpers import (
    get_optimized_scripts_for_templates,
    format_template_script_display,
    get_available_test_cases,
    format_test_case_display,
    validate_template_generation_inputs,
    create_template_generation_filename,
    extract_template_structure_info
)

from core.template_prompt_builder import (
    generate_template_based_script_prompt,
    enhance_template_prompt_with_context
)

# Configure logging
logger = logging.getLogger("ScriptWeaver.stage10")


def stage10_script_playground(state):
    """
    Stage 10: Script Playground.

    This stage provides an independent, always-accessible script experimentation and generation utility.
    It loads optimized scripts as templates and generates new scripts for different test cases,
    preserving the optimization patterns and best practices from the templates.

    Args:
        state (StateManager): The application state manager instance
    """
    st.markdown("<h2 class='stage-header'>🎮 Script Playground</h2>", unsafe_allow_html=True)
    st.markdown("*Experiment with script generation using optimized scripts as templates*")

    debug("Stage 10: Script Playground accessed")

    # Initialize script storage if needed
    if not hasattr(state, '_script_storage') or state._script_storage is None:
        state._init_script_storage()

    # Load optimized scripts for templates
    optimized_scripts = get_optimized_scripts_for_templates(state._script_storage)
    available_test_cases = get_available_test_cases(state)

    # Check if we have the necessary data
    if not optimized_scripts:
        st.info("🎮 **Script Playground is Empty**")
        st.markdown("""
        **Welcome to the Script Playground!** This experimental environment allows you to generate new test scripts using optimized scripts as templates.

        **To get started:**
        - 📁 Complete the workflow through Stage 8 to create optimized scripts
        - ⚙️ Optimized scripts from Stage 8 will appear here as templates
        - 🎮 Use templates to experiment with script generation for different test cases

        **What you can do here:**
        - 🎯 Select optimized scripts as templates
        - 📋 Choose target test cases for generation
        - 🤖 Generate new scripts using AI with template patterns
        - 📥 Download and manage generated scripts
        """)

        # Provide navigation to create templates
        st.markdown("### 🚀 Create Templates")
        col1, col2, col3 = st.columns(3)
        with col1:
            if st.button("📁 Start New Project", use_container_width=True, type="primary"):
                state.advance_to(StateStage.STAGE1_UPLOAD, "User started new project from Script Playground")
                st.rerun()
                return
        with col2:
            if st.button("⚙️ Generate Scripts", use_container_width=True):
                state.advance_to(StateStage.STAGE6_GENERATE, "User navigated to script generation from Script Playground")
                st.rerun()
                return
        with col3:
            if st.button("🔧 Optimize Scripts", use_container_width=True):
                state.advance_to(StateStage.STAGE8_OPTIMIZE, "User navigated to optimization from Script Playground")
                st.rerun()
                return
        return

    if not available_test_cases:
        st.warning("⚠️ **No Test Cases Available**")
        st.markdown("""
        You have optimized templates available, but no test cases to generate scripts for.
        Please upload a CSV file with test cases first.
        """)

        if st.button("📁 Upload Test Cases", use_container_width=True, type="primary"):
            state.advance_to(StateStage.STAGE1_UPLOAD, "User navigated to upload from Script Playground")
            st.rerun()
            return
        return

    # Template Selection Section
    with st.expander("🎯 Template Selection", expanded=True):
        st.markdown("**Select an optimized script to use as a template:**")

        # Create template options
        template_options = []
        template_map = {}

        for script in optimized_scripts:
            display_info = format_template_script_display(script)
            option_text = f"{display_info['title']} - {display_info['timestamp']}"
            template_options.append(option_text)
            template_map[option_text] = script

        if template_options:
            selected_template_option = st.selectbox(
                "Available Templates",
                template_options,
                key="template_selection"
            )

            selected_template = template_map[selected_template_option]

            # Display template details
            col1, col2 = st.columns(2)
            with col1:
                st.info(f"""
                **Template Details:**
                - Test Case: {selected_template.get('test_case_id', 'Unknown')}
                - Created: {format_template_script_display(selected_template)['timestamp']}
                - Size: {format_template_script_display(selected_template)['size_info']}
                """)

            with col2:
                st.info(f"""
                **Optimization Info:**
                - Status: ✅ Optimized
                - Type: {selected_template.get('type', 'Unknown').title()}
                - {format_template_script_display(selected_template)['optimization_info']}
                """)

            # Template preview toggle
            if st.checkbox("📄 Show Template Preview", key="show_template_preview"):
                st.markdown("**Template Code:**")
                template_content = selected_template.get('content', 'No content available')
                st.code(template_content, language='python')

    # Test Case Selection Section
    with st.expander("📋 Target Test Case Selection", expanded=True):
        st.markdown("**Select a test case to generate a script for:**")

        # Create test case options
        test_case_options = []
        test_case_map = {}

        for test_case in available_test_cases:
            option_text = format_test_case_display(test_case)
            test_case_options.append(option_text)
            test_case_map[option_text] = test_case

        if test_case_options:
            selected_test_case_option = st.selectbox(
                "Available Test Cases",
                test_case_options,
                key="test_case_selection"
            )

            selected_test_case = test_case_map[selected_test_case_option]

            # Display test case details
            tc_id = selected_test_case.get('Test Case ID', 'Unknown')
            tc_objective = selected_test_case.get('Test Case Objective', 'No objective specified')
            tc_steps = selected_test_case.get('Steps', [])

            st.info(f"""
            **Target Test Case:**
            - ID: {tc_id}
            - Objective: {tc_objective}
            - Steps: {len(tc_steps)} steps defined
            """)

    # Template-Based Generation Section
    with st.expander("🤖 Template-Based Generation", expanded=True):
        st.markdown("**Generate a new script using the selected template and test case:**")

        # Validate inputs
        if 'selected_template' in locals() and 'selected_test_case' in locals():
            is_valid, error_message = validate_template_generation_inputs(selected_template, selected_test_case)

            if not is_valid:
                st.error(f"❌ **Validation Error**: {error_message}")
                return

            # Generation controls
            col1, col2 = st.columns(2)

            with col1:
                custom_instructions = st.text_area(
                    "Custom Instructions (Optional)",
                    placeholder="Add any specific requirements or modifications...",
                    key="template_custom_instructions"
                )

            with col2:
                st.markdown("**Generation Settings:**")
                preserve_structure = st.checkbox("Preserve Template Structure", value=True, key="preserve_structure")
                include_error_handling = st.checkbox("Include Error Handling", value=True, key="include_error_handling")

            # Generate button
            if st.button("🚀 Generate Script from Template", use_container_width=True, type="primary"):
                _generate_script_from_template(
                    state, selected_template, selected_test_case,
                    custom_instructions, preserve_structure, include_error_handling
                )
        else:
            st.info("👆 Please select both a template and target test case above to enable generation.")

    # Script Execution Section (Independent from generation expander)
    _display_script_execution_section_if_available(state)

    # Script Playground Footer
    st.markdown("---")
    st.markdown("### 🎮 Script Playground Information")

    col1, col2 = st.columns(2)

    with col1:
        st.info("""
        **🔄 Always Accessible**: This Script Playground is available at any time, regardless of your current workflow stage.

        **🎯 Template-Based**: Experiment with script generation using proven optimization patterns from existing optimized scripts.
        """)

    with col2:
        st.info("""
        **🤖 AI-Powered**: Uses Google AI to adapt templates while preserving best practices and optimization patterns.

        **⚡ Independent**: Use this experimental environment without affecting your current workflow progress.
        """)

    # Optional workflow navigation (non-intrusive)
    with st.expander("🧭 Quick Workflow Navigation", expanded=False):
        st.markdown("*Optional: Jump to other workflow stages if needed*")

        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("📁 Upload CSV (Stage 1)", use_container_width=True):
                debug("User navigated to Stage 1 from Script Playground")
                state.advance_to(StateStage.STAGE1_UPLOAD, "User navigated to Stage 1 from Script Playground")
                st.rerun()

        with col2:
            if st.button("🔧 Optimize Scripts (Stage 8)", use_container_width=True):
                debug("User navigated to Stage 8 from Script Playground")
                state.advance_to(StateStage.STAGE8_OPTIMIZE, "User navigated to Stage 8 from Script Playground")
                st.rerun()

        with col3:
            if st.button("📜 Script Browser (Stage 9)", use_container_width=True):
                debug("User navigated to Stage 9 from Script Playground")
                state.advance_to(StateStage.STAGE9_BROWSE, "User navigated to Stage 9 from Script Playground")
                st.rerun()



def _generate_script_from_template(state, template_script, target_test_case,
                                 custom_instructions, preserve_structure, include_error_handling):
    """
    Generate a new script using the selected template and test case.

    Args:
        state: StateManager instance
        template_script: Selected template script
        target_test_case: Target test case for generation
        custom_instructions: User's custom instructions
        preserve_structure: Whether to preserve template structure
        include_error_handling: Whether to include error handling
    """
    try:
        debug("Starting template-based script generation")

        with st.spinner("🤖 Generating script from template..."):
            # Extract template structure information
            template_structure_info = extract_template_structure_info(template_script.get('content', ''))

            # Generate the prompt
            base_prompt = generate_template_based_script_prompt(
                template_script=template_script,
                target_test_case=target_test_case,
                template_structure_info=template_structure_info,
                website_url=getattr(state, 'website_url', None)
            )

            # Enhance prompt with additional context
            additional_context = {
                'custom_instructions': custom_instructions if custom_instructions else None,
                'preserve_structure': preserve_structure,
                'include_error_handling': include_error_handling
            }

            enhanced_prompt = enhance_template_prompt_with_context(base_prompt, additional_context)

            # Generate script using Google AI
            debug("Calling Google AI for template-based script generation")
            generated_script = generate_llm_response(
                prompt=enhanced_prompt,
                model_name="gemini-2.0-flash",
                api_key=getattr(state, 'google_api_key', None),
                category="template_script_generation",
                context={
                    'template_test_case_id': template_script.get('test_case_id', 'unknown'),
                    'target_test_case_id': target_test_case.get('Test Case ID', 'unknown'),
                    'template_script_id': template_script.get('id', 'unknown'),
                    'generation_type': 'template_based'
                }
            )

            if generated_script and generated_script.strip():
                _handle_successful_generation(state, template_script, target_test_case, generated_script)
            else:
                st.error("❌ Failed to generate script. Please try again.")
                debug("Template-based script generation failed - empty response")

    except Exception as e:
        error_msg = f"Template-based script generation failed: {e}"
        st.error(f"❌ **Generation Error**: {error_msg}")
        debug(f"Template-based script generation error: {e}")


def _handle_successful_generation(state, template_script, target_test_case, generated_script):
    """
    Handle successful script generation with display, storage, and execution.

    Args:
        state: StateManager instance
        template_script: Template script used
        target_test_case: Target test case
        generated_script: Generated script content
    """
    try:
        debug("Handling successful template-based script generation")

        # Parse the generated script to extract clean Python code from markdown
        debug(f"Raw generated script length: {len(generated_script)} characters")
        parsed_script = clean_llm_response(generated_script, "python")
        debug(f"Parsed script length: {len(parsed_script)} characters")

        # Create filename
        filename = create_template_generation_filename(template_script, target_test_case)

        # Display success message
        st.success("✅ **Script Generated Successfully!**")

        # Display parsed script (clean Python code)
        st.markdown("### 📄 Generated Script")
        st.code(parsed_script, language='python')

        # Download and copy buttons
        col1, col2 = st.columns(2)
        with col1:
            st.download_button(
                label="📥 Download Script",
                data=parsed_script,
                file_name=filename,
                mime="text/x-python",
                use_container_width=True
            )

        with col2:
            if st.button("📋 Copy to Clipboard", use_container_width=True):
                st.code(parsed_script)  # This allows easy copying
                st.info("Script displayed above for copying")

        # Save to script storage
        template_metadata = {
            'generation_type': 'template_based',
            'template_script_id': template_script.get('id'),
            'template_test_case_id': template_script.get('test_case_id'),
            'target_test_case_id': target_test_case.get('Test Case ID'),
            'generation_timestamp': datetime.now().isoformat(),
            'template_based': True,
            'optimization_status': 'template_generated'
        }

        state.add_script_to_history(
            script_content=parsed_script,
            script_type='template_generated',
            step_no=None,
            file_path=filename,
            metadata=template_metadata
        )

        debug(f"Template-based script saved with filename: {filename}")

        # Display generation summary
        st.info(f"""
        **Generation Summary:**
        - Template: {template_script.get('test_case_id', 'Unknown')}
        - Target: {target_test_case.get('Test Case ID', 'Unknown')}
        - Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        - Filename: {filename}
        """)

        # Store generation data in session state for execution section (using parsed script)
        st.session_state['stage10_generated_script'] = {
            'script_content': parsed_script,
            'filename': filename,
            'target_test_case': target_test_case,
            'template_script': template_script,
            'generation_timestamp': datetime.now().isoformat(),
            'raw_generated_content': generated_script  # Keep original for debugging if needed
        }

        debug(f"Stored generated script data in session state for execution: {filename}")

    except Exception as e:
        error_msg = f"Failed to handle successful generation: {e}"
        st.error(f"❌ **Storage Error**: {error_msg}")
        debug(f"Error handling successful generation: {e}")


def _display_script_execution_section_if_available(state):
    """
    Display script execution section with enhanced professional UI design.

    This function provides a polished, enterprise-grade interface for script execution
    with improved visual hierarchy, status indicators, and user experience.

    Args:
        state: StateManager instance
    """
    try:
        # Check if we have a generated script in session state
        if 'stage10_generated_script' not in st.session_state:
            return

        script_data = st.session_state['stage10_generated_script']
        generated_script = script_data.get('script_content')
        filename = script_data.get('filename')
        target_test_case = script_data.get('target_test_case')

        if not all([generated_script, filename, target_test_case]):
            debug("Incomplete script data in session state, skipping execution section")
            return

        debug(f"Displaying execution section for generated script: {filename}")

        # Professional section header with enhanced styling
        st.markdown("---")
        st.markdown("""
        <div style="text-align: center; margin: 2rem 0;">
            <h2 style="color: var(--primary-color); font-weight: 700; margin-bottom: 0.5rem;">
                🧪 Test Generated Script
            </h2>
            <p style="color: var(--text-color-light); font-size: 1.1rem; margin: 0;">
                Execute and validate your generated automation script
            </p>
        </div>
        """, unsafe_allow_html=True)

        # Check execution status for status indicators
        execution_key = f"stage10_execution_results_{filename}"
        has_test_results = execution_key in st.session_state
        execution_status = "ready"

        if has_test_results:
            test_results = st.session_state[execution_key]
            execution_status = "passed" if test_results.get('success', False) else "failed"

        # Script Information Card with Professional Styling
        _render_script_info_card(script_data, target_test_case, execution_status)

        # Execution Controls Section
        _render_execution_controls(state, script_data, execution_status, verbose_mode_key=f"stage10_verbose_{filename}")

        # Display execution results if available
        if has_test_results:
            test_results = st.session_state[execution_key]
            _display_execution_results(test_results, target_test_case)

    except Exception as e:
        error_msg = f"Failed to display script execution section: {e}"
        st.error(f"❌ **Execution Display Error**: {error_msg}")
        debug(f"Error displaying script execution section: {e}")


def _render_script_info_card(script_data, target_test_case, execution_status):
    """
    Render a professional script information card with status indicators.

    Args:
        script_data: Script data from session state
        target_test_case: Target test case information
        execution_status: Current execution status (ready, passed, failed)
    """
    # Status indicator styling
    status_config = {
        "ready": {
            "icon": "⏳",
            "color": "#2196F3",
            "bg_color": "rgba(33, 150, 243, 0.1)",
            "text": "Ready for Execution"
        },
        "passed": {
            "icon": "✅",
            "color": "#4CAF50",
            "bg_color": "rgba(76, 175, 80, 0.1)",
            "text": "Execution Passed"
        },
        "failed": {
            "icon": "❌",
            "color": "#F44336",
            "bg_color": "rgba(244, 67, 54, 0.1)",
            "text": "Execution Failed"
        }
    }

    status = status_config.get(execution_status, status_config["ready"])
    filename = script_data.get('filename', 'Unknown')
    tc_id = target_test_case.get('Test Case ID', 'Unknown')
    generation_time = script_data.get('generation_timestamp', 'Unknown')

    # Format timestamp for better display
    if generation_time != 'Unknown':
        try:
            from datetime import datetime
            dt = datetime.fromisoformat(generation_time.replace('Z', '+00:00'))
            generation_time = dt.strftime('%Y-%m-%d %H:%M:%S')
        except:
            pass

    # Status indicator with enhanced styling
    status_color_map = {
        "ready": "🔵",
        "passed": "✅",
        "failed": "❌"
    }

    status_icon = status_color_map.get(execution_status, "🔵")
    status_text = status['text']

    # Display status badge
    if execution_status == "ready":
        st.info(f"{status_icon} {status_text}")
    elif execution_status == "passed":
        st.success(f"{status_icon} {status_text}")
    elif execution_status == "failed":
        st.error(f"{status_icon} {status_text}")

    # Script information using Streamlit columns to avoid HTML rendering issues
    st.markdown("### � Script Information")

    col1, col2 = st.columns(2)

    with col1:
        st.markdown("#### 📄 Script Details")
        st.markdown(f"""
        **Filename:** `{filename}`
        **Generated:** {generation_time}
        """)

    with col2:
        st.markdown("#### 🎯 Target Test Case")
        objective = target_test_case.get('Test Case Objective', 'Not specified')
        truncated_objective = objective[:60] + '...' if len(objective) > 60 else objective
        st.markdown(f"""
        **Test Case ID:** {tc_id}
        **Objective:** {truncated_objective}
        """)


def _render_execution_controls(state, script_data, execution_status, verbose_mode_key):
    """
    Render enhanced execution controls with professional styling and improved layout.

    Args:
        state: StateManager instance
        script_data: Script data from session state
        execution_status: Current execution status
        verbose_mode_key: Key for verbose mode checkbox
    """
    filename = script_data.get('filename')
    generated_script = script_data.get('script_content')
    target_test_case = script_data.get('target_test_case')
    execution_key = f"stage10_execution_results_{filename}"

    # Professional execution controls header
    st.markdown("""
    <div style="margin: 2rem 0 1.5rem 0;">
        <h3 style="color: var(--primary-color); margin-bottom: 0.5rem; font-size: 1.4rem; font-weight: 700;">
            ⚙️ Execution Controls
        </h3>
        <p style="color: var(--text-color-light); margin: 0; font-size: 1rem; opacity: 0.8;">
            Configure and execute your generated automation script
        </p>
    </div>
    """, unsafe_allow_html=True)

    # Execution options section with professional card styling
    st.markdown("""
    <div class="pro-card" style="margin: 1rem 0; padding: 1.5rem;">
        <h4 style="color: var(--primary-color); margin: 0 0 1rem 0; font-size: 1.1rem;">
            🔧 Execution Options
        </h4>
    </div>
    """, unsafe_allow_html=True)

    # Verbose mode checkbox with better spacing
    verbose_mode = st.checkbox(
        "Enable Verbose Mode",
        value=False,
        key=verbose_mode_key,
        help="Show detailed execution output, logs, and debugging information for comprehensive analysis"
    )

    # Status indicator with enhanced styling
    status_messages = {
        "ready": {
            "icon": "💡",
            "text": "Script is ready for execution",
            "type": "info",
            "color": "#2196F3"
        },
        "passed": {
            "icon": "✅",
            "text": "Last execution was successful",
            "type": "success",
            "color": "#4CAF50"
        },
        "failed": {
            "icon": "❌",
            "text": "Last execution failed - check results below",
            "type": "error",
            "color": "#F44336"
        }
    }

    if execution_status in status_messages:
        status_info = status_messages[execution_status]
        st.markdown(f"""
        <div style="
            background: rgba({','.join(str(int(status_info['color'][i:i+2], 16)) for i in (1, 3, 5))}, 0.1);
            border: 1px solid {status_info['color']}40;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            display: flex;
            align-items: center;
            gap: 0.8rem;
        ">
            <span style="font-size: 1.2rem;">{status_info['icon']}</span>
            <span style="color: {status_info['color']}; font-weight: 600;">{status_info['text']}</span>
        </div>
        """, unsafe_allow_html=True)

    # Action buttons section with improved layout
    st.markdown("""
    <div style="margin: 2rem 0 1rem 0;">
        <h4 style="color: var(--primary-color); margin: 0 0 1rem 0; font-size: 1.1rem;">
            🚀 Actions
        </h4>
    </div>
    """, unsafe_allow_html=True)

    # Enhanced button layout with better spacing
    if execution_status in ["passed", "failed"]:
        # Show all three buttons when script has been executed
        button_col1, button_col2, button_col3 = st.columns([1, 1, 1])

        with button_col1:
            button_type = "primary" if execution_status == "ready" else "secondary"
            if st.button(
                "🚀 Execute Script",
                use_container_width=True,
                type=button_type,
                key=f"execute_{filename}",
                help="Run the generated script and view results"
            ):
                _execute_generated_script(state, generated_script, filename, target_test_case, verbose_mode)

        with button_col2:
            if st.button(
                "🔄 Re-run Script",
                use_container_width=True,
                key=f"rerun_{filename}",
                help="Execute the script again with current settings"
            ):
                _execute_generated_script(state, generated_script, filename, target_test_case, verbose_mode)

        with button_col3:
            # Minimalist icon button for clearing script
            if st.button(
                "🗑️",
                use_container_width=True,
                key=f"clear_{filename}",
                help="Clear script and execution results",
                type="secondary"
            ):
                # Clear the generated script from session state
                if 'stage10_generated_script' in st.session_state:
                    del st.session_state['stage10_generated_script']
                # Clear execution results
                if execution_key in st.session_state:
                    del st.session_state[execution_key]
                st.success("✅ Script cleared successfully!")
                st.rerun()
    else:
        # Show only execute and clear buttons for ready state
        button_col1, button_col2 = st.columns([2, 1])

        with button_col1:
            if st.button(
                "🚀 Execute Script",
                use_container_width=True,
                type="primary",
                key=f"execute_{filename}",
                help="Run the generated script and view results"
            ):
                _execute_generated_script(state, generated_script, filename, target_test_case, verbose_mode)

        with button_col2:
            # Minimalist icon button for clearing script
            if st.button(
                "🗑️",
                use_container_width=True,
                key=f"clear_{filename}",
                help="Clear script and execution results",
                type="secondary"
            ):
                # Clear the generated script from session state
                if 'stage10_generated_script' in st.session_state:
                    del st.session_state['stage10_generated_script']
                # Clear execution results
                if execution_key in st.session_state:
                    del st.session_state[execution_key]
                st.success("✅ Script cleared successfully!")
                st.rerun()


def _execute_script_with_conftest(script_path, filename, target_test_case, verbose_mode=False):
    """
    Execute a generated script with proper conftest.py integration and pytest configuration.

    This function ensures that:
    1. The script runs from the correct working directory (GretahAI_ScriptWeaver)
    2. The conftest.py file is accessible for browser fixture
    3. Required pytest plugins are handled gracefully
    4. Proper error handling for missing dependencies
    5. Temporary script is copied to project directory to ensure proper rootdir detection

    Args:
        script_path: Path to the temporary script file
        filename: Original filename for logging
        target_test_case: Target test case information
        verbose_mode: Whether to use verbose execution mode

    Returns:
        dict: Test execution results
    """
    try:
        debug(f"Executing script with conftest integration: {filename}")

        # Import required modules
        import subprocess
        import os
        import shutil
        from datetime import datetime
        from pathlib import Path

        # Determine the correct working directory (GretahAI_ScriptWeaver root)
        current_dir = os.getcwd()
        scriptweaver_dir = None

        # Check if we're already in GretahAI_ScriptWeaver directory
        if "GretahAI_ScriptWeaver" in current_dir:
            if current_dir.endswith("GretahAI_ScriptWeaver"):
                scriptweaver_dir = current_dir
            else:
                # Navigate to GretahAI_ScriptWeaver directory
                parts = current_dir.split(os.sep)
                try:
                    scriptweaver_index = parts.index("GretahAI_ScriptWeaver")
                    scriptweaver_dir = os.sep.join(parts[:scriptweaver_index + 1])
                except ValueError:
                    scriptweaver_dir = current_dir
        else:
            # Fallback to current directory
            scriptweaver_dir = current_dir

        debug(f"Using working directory: {scriptweaver_dir}")

        # Verify conftest.py exists
        conftest_path = os.path.join(scriptweaver_dir, "conftest.py")
        if not os.path.exists(conftest_path):
            debug(f"Warning: conftest.py not found at {conftest_path}")
            st.warning("⚠️ conftest.py not found - browser fixture may not be available")
        else:
            debug(f"Found conftest.py at {conftest_path}")

        # CRITICAL FIX: Copy temporary script to GretahAI_ScriptWeaver directory
        # This ensures pytest uses the correct rootdir where conftest.py is located
        local_script_name = f"temp_stage10_script_{datetime.now().strftime('%Y%m%d_%H%M%S')}.py"
        local_script_path = os.path.join(scriptweaver_dir, local_script_name)

        debug(f"Copying script from {script_path} to {local_script_path}")
        shutil.copy2(script_path, local_script_path)

        # Use the local script path for execution
        execution_script_path = local_script_path

        # Set environment variables for the test run
        env = os.environ.copy()
        env["HEADLESS"] = "0"  # Always run in visible mode for Stage 10
        env["PYTEST_QUIET_MODE"] = "1" if not verbose_mode else "0"

        # Generate timestamped result file name
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        result_xml_path = os.path.join(scriptweaver_dir, f"results_stage10_{timestamp}.xml")

        # Build pytest command with proper configuration
        pytest_command = [
            "pytest",
            execution_script_path,  # Use local script path
            f"--junitxml={result_xml_path}",
            "--tb=short",  # Short traceback format
            "-v" if verbose_mode else "-q",  # Verbose or quiet mode
            "--capture=no",  # Don't capture output for better debugging
            f"--rootdir={scriptweaver_dir}",  # Explicitly set rootdir
        ]

        # Add pytest-order plugin if available, otherwise warn and continue
        try:
            import pytest_order
            debug("pytest-order plugin is available")
        except ImportError:
            debug("pytest-order plugin not available - order decorators will be ignored")
            st.warning("⚠️ pytest-order plugin not installed - test execution order may not be preserved")
            # Add option to ignore unknown markers
            pytest_command.extend(["--disable-warnings", "-p", "no:warnings"])

        debug(f"Executing pytest command: {' '.join(pytest_command)}")
        debug(f"Working directory: {scriptweaver_dir}")
        debug(f"Script path: {execution_script_path}")
        debug(f"Conftest path: {conftest_path}")

        # Execute the test script
        result = subprocess.run(
            pytest_command,
            capture_output=True,
            text=True,
            env=env,
            cwd=scriptweaver_dir  # This is crucial for conftest.py access
        )

        debug(f"Pytest execution completed with return code: {result.returncode}")
        debug(f"Stdout length: {len(result.stdout)} characters")
        debug(f"Stderr length: {len(result.stderr)} characters")

        # Parse JUnit XML results if available
        xml_results = None
        performance_metrics = {}
        artifacts = {}

        if os.path.exists(result_xml_path):
            try:
                from core.junit_parser import parse_junit_xml, format_test_results_for_display
                xml_results = parse_junit_xml(result_xml_path)
                if xml_results:
                    formatted_results = format_test_results_for_display(xml_results)
                    performance_metrics = formatted_results.get("performance_summary", {})

                    # Extract artifacts from test details
                    for test_detail in formatted_results.get("test_details", []):
                        test_artifacts = test_detail.get("artifacts", {})
                        if test_artifacts:
                            artifacts.update(test_artifacts)
                debug(f"Successfully parsed JUnit XML results")
            except Exception as e:
                debug(f"Failed to parse JUnit XML: {e}")
        else:
            debug(f"JUnit XML file not found: {result_xml_path}")

        # Check for screenshots
        screenshots = []
        screenshots_dir = Path(scriptweaver_dir) / "screenshots"
        if screenshots_dir.exists():
            screenshot_files = list(screenshots_dir.glob("*.png"))
            screenshot_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            screenshots = [str(f) for f in screenshot_files[:5]]  # Get up to 5 most recent
            debug(f"Found {len(screenshots)} screenshots")

        # Prepare the results dictionary
        execution_results = {
            "stdout": result.stdout,
            "stderr": result.stderr,
            "returncode": result.returncode,
            "success": result.returncode == 0,
            "screenshots": screenshots,
            "script_path": script_path,
            "test_context": f"Stage 10 generated script ({filename})",
            "xml_path": result_xml_path if os.path.exists(result_xml_path) else None,
            "xml_results": xml_results,
            "performance_metrics": performance_metrics,
            "artifacts": artifacts,
            "timestamp": timestamp,
            "working_directory": scriptweaver_dir,
            "conftest_available": os.path.exists(conftest_path),
            "local_script_path": local_script_path  # Store for cleanup
        }

        debug(f"Script execution completed successfully")

        # Clean up the temporary local script file
        try:
            if os.path.exists(local_script_path):
                os.unlink(local_script_path)
                debug(f"Cleaned up temporary local script: {local_script_path}")
        except Exception as cleanup_error:
            debug(f"Failed to clean up temporary local script: {cleanup_error}")

        return execution_results

    except Exception as e:
        error_msg = f"Failed to execute script with conftest integration: {e}"
        debug(error_msg)

        # Clean up the temporary local script file if it was created
        try:
            if 'local_script_path' in locals() and os.path.exists(local_script_path):
                os.unlink(local_script_path)
                debug(f"Cleaned up temporary local script after error: {local_script_path}")
        except Exception as cleanup_error:
            debug(f"Failed to clean up temporary local script after error: {cleanup_error}")

        # Return error results
        return {
            "stdout": "",
            "stderr": error_msg,
            "returncode": -1,
            "success": False,
            "screenshots": [],
            "script_path": script_path,
            "test_context": f"Stage 10 generated script ({filename})",
            "xml_path": None,
            "xml_results": None,
            "performance_metrics": {},
            "artifacts": {},
            "timestamp": datetime.now().strftime("%Y%m%d_%H%M%S"),
            "error": str(e),
            "working_directory": os.getcwd(),
            "conftest_available": False
        }


def _execute_generated_script(state, generated_script, filename, target_test_case, verbose_mode=False):
    """
    Execute the generated script using the same infrastructure as Stage 7/8.

    Args:
        state: StateManager instance
        generated_script: Generated script content
        filename: Script filename
        target_test_case: Target test case information
        verbose_mode: Whether to use verbose execution mode
    """
    try:
        debug(f"Executing generated script: {filename}")

        # Create a temporary file for the script
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as temp_file:
            temp_file.write(generated_script)
            temp_script_path = temp_file.name

        debug(f"Created temporary script file: {temp_script_path}")

        with st.spinner(f"🧪 Executing generated script for {target_test_case.get('Test Case ID', 'Unknown')}..."):
            try:
                # Execute the script using enhanced Stage 10 execution method
                test_results = _execute_script_with_conftest(
                    script_path=temp_script_path,
                    filename=filename,
                    target_test_case=target_test_case,
                    verbose_mode=verbose_mode
                )

                # Store results in session state for persistence
                execution_key = f"stage10_execution_results_{filename}"
                st.session_state[execution_key] = test_results

                debug(f"Script execution completed with success: {test_results.get('success', False)}")

                # Display immediate feedback
                if test_results.get('success', False):
                    st.success("✅ Generated script executed successfully!")
                else:
                    st.error("❌ Generated script execution failed. Check the results below.")

                # Display the detailed results
                _display_execution_results(test_results, target_test_case, verbose_mode)

            finally:
                # Clean up temporary file
                try:
                    os.unlink(temp_script_path)
                    debug(f"Cleaned up temporary script file: {temp_script_path}")
                except Exception as cleanup_error:
                    debug(f"Failed to clean up temporary file: {cleanup_error}")

    except Exception as e:
        error_msg = f"Script execution failed: {e}"
        st.error(f"❌ **Execution Error**: {error_msg}")
        debug(f"Error executing generated script: {e}")
        import traceback
        debug(f"Execution traceback: {traceback.format_exc()}")


def _display_execution_results(test_results, target_test_case, verbose_mode=False):
    """
    Display script execution results with enhanced professional UI design spanning full width.

    This function creates a comprehensive, enterprise-grade execution results display
    that utilizes the full available screen width for optimal information presentation.

    Args:
        test_results: Test execution results dictionary
        target_test_case: Target test case information
        verbose_mode: Whether to show verbose details
    """
    try:
        debug("Displaying execution results in Stage 10 with full-width layout")

        # Basic execution status
        success = test_results.get('success', False)
        returncode = test_results.get('returncode', -1)
        timestamp = test_results.get('timestamp', 'Unknown')

        # Enhanced results header with professional styling - full width
        st.markdown("---")
        st.markdown("""
        <div style="text-align: center; margin: 2rem 0 1.5rem 0; width: 100%;">
            <h2 style="color: var(--primary-color); font-weight: 700; margin-bottom: 0.5rem; font-size: 1.8rem;">
                📊 Execution Results
            </h2>
            <p style="color: var(--text-color-light); font-size: 1.1rem; margin: 0; opacity: 0.8;">
                Comprehensive test execution analysis and performance metrics
            </p>
        </div>
        """, unsafe_allow_html=True)

        # Results summary card with status-based styling - full width layout
        status_color = "#4CAF50" if success else "#F44336"
        status_bg = "rgba(76, 175, 80, 0.1)" if success else "rgba(244, 67, 54, 0.1)"
        status_icon = "✅" if success else "❌"
        status_text = "PASSED" if success else "FAILED"

        # Format timestamp for better display
        formatted_timestamp = timestamp
        if timestamp != 'Unknown':
            try:
                from datetime import datetime
                if isinstance(timestamp, str):
                    dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                    formatted_timestamp = dt.strftime('%Y-%m-%d %H:%M:%S')
                else:
                    formatted_timestamp = str(timestamp)
            except:
                formatted_timestamp = str(timestamp)

        st.markdown(f"""
        <div style="
            background: linear-gradient(135deg, {status_bg}, rgba(255,255,255,0.05));
            border: 1px solid {status_color}40;
            border-radius: 12px;
            padding: 1.5rem;
            margin: 1.5rem 0;
            width: 100%;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        ">
            <div style="display: grid; grid-template-columns: auto 1fr auto; gap: 2rem; align-items: center;">
                <div style="
                    background: {status_bg};
                    color: {status_color};
                    padding: 0.8rem 1.8rem;
                    border-radius: 30px;
                    font-weight: 700;
                    font-size: 1.2rem;
                    display: flex;
                    align-items: center;
                    gap: 0.8rem;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                ">
                    <span style="font-size: 1.4rem;">{status_icon}</span>
                    {status_text}
                </div>
                <div style="text-align: center; color: var(--text-color-light);">
                    <div style="font-size: 1rem; opacity: 0.8; margin-bottom: 0.3rem;">Test Case</div>
                    <div style="font-weight: 600; font-size: 1.1rem; color: var(--primary-color);">{target_test_case.get('Test Case ID', 'Unknown')}</div>
                </div>
                <div style="text-align: right; color: var(--text-color-light);">
                    <div style="font-size: 0.9rem; opacity: 0.8; margin-bottom: 0.3rem;">Completed</div>
                    <div style="font-weight: 600; font-size: 1rem;">{formatted_timestamp}</div>
                </div>
            </div>
        </div>
        """, unsafe_allow_html=True)

        # Execution metrics in professional card layout - full width
        st.markdown("""
        <div style="margin: 2rem 0 1rem 0;">
            <h3 style="color: var(--primary-color); margin-bottom: 0.5rem; font-size: 1.4rem; font-weight: 700;">
                📈 Execution Metrics
            </h3>
            <p style="color: var(--text-color-light); margin: 0; font-size: 1rem; opacity: 0.8;">
                Detailed performance and execution statistics
            </p>
        </div>
        """, unsafe_allow_html=True)

        # Parse and display JUnit XML results if available
        xml_results = test_results.get('xml_results')
        if xml_results and "summary" in xml_results:
            summary = xml_results["summary"]

            # Display metrics using responsive grid layout for full width
            total_tests = summary.get("total_tests", 0)
            passed_tests = summary.get("passed_tests", 0)
            failed_tests = summary.get("failed_tests", 0)
            duration = summary.get("duration", 0)

            st.markdown(f"""
            <div style="
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 1.5rem;
                margin: 1.5rem 0;
                width: 100%;
            ">
                <div style="
                    text-align: center;
                    padding: 1.5rem;
                    background: linear-gradient(135deg, rgba(103, 58, 183, 0.08), rgba(103, 58, 183, 0.03));
                    border-radius: 12px;
                    border: 1px solid rgba(103, 58, 183, 0.2);
                    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
                ">
                    <div style="font-size: 2rem; font-weight: bold; color: var(--primary-color); margin-bottom: 0.5rem;">{total_tests}</div>
                    <div style="font-size: 1rem; color: var(--text-color-light); font-weight: 600;">Total Tests</div>
                </div>
                <div style="
                    text-align: center;
                    padding: 1.5rem;
                    background: linear-gradient(135deg, rgba(76, 175, 80, 0.08), rgba(76, 175, 80, 0.03));
                    border-radius: 12px;
                    border: 1px solid rgba(76, 175, 80, 0.2);
                    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
                ">
                    <div style="font-size: 2rem; font-weight: bold; color: #4CAF50; margin-bottom: 0.5rem;">{passed_tests}</div>
                    <div style="font-size: 1rem; color: var(--text-color-light); font-weight: 600;">Passed</div>
                </div>
                <div style="
                    text-align: center;
                    padding: 1.5rem;
                    background: linear-gradient(135deg, rgba(244, 67, 54, 0.08), rgba(244, 67, 54, 0.03));
                    border-radius: 12px;
                    border: 1px solid rgba(244, 67, 54, 0.2);
                    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
                ">
                    <div style="font-size: 2rem; font-weight: bold; color: #F44336; margin-bottom: 0.5rem;">{failed_tests}</div>
                    <div style="font-size: 1rem; color: var(--text-color-light); font-weight: 600;">Failed</div>
                </div>
                <div style="
                    text-align: center;
                    padding: 1.5rem;
                    background: linear-gradient(135deg, rgba(33, 150, 243, 0.08), rgba(33, 150, 243, 0.03));
                    border-radius: 12px;
                    border: 1px solid rgba(33, 150, 243, 0.2);
                    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
                ">
                    <div style="font-size: 2rem; font-weight: bold; color: #2196F3; margin-bottom: 0.5rem;">{duration:.2f}s</div>
                    <div style="font-size: 1rem; color: var(--text-color-light); font-weight: 600;">Duration</div>
                </div>
            </div>
            """, unsafe_allow_html=True)
        else:
            # Basic metrics when JUnit results are not available - using responsive grid layout
            # Format timestamp for better display
            if timestamp != 'Unknown':
                try:
                    from datetime import datetime
                    if isinstance(timestamp, str):
                        dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                        formatted_time = dt.strftime('%H:%M:%S')
                    else:
                        formatted_time = timestamp
                except:
                    formatted_time = str(timestamp)
            else:
                formatted_time = 'Unknown'

            st.markdown(f"""
            <div style="
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 1.5rem;
                margin: 1.5rem 0;
                width: 100%;
            ">
                <div style="
                    text-align: center;
                    padding: 1.5rem;
                    background: linear-gradient(135deg, rgba(103, 58, 183, 0.08), rgba(103, 58, 183, 0.03));
                    border-radius: 12px;
                    border: 1px solid rgba(103, 58, 183, 0.2);
                    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
                ">
                    <div style="font-size: 2rem; font-weight: bold; color: var(--primary-color); margin-bottom: 0.5rem;">{returncode}</div>
                    <div style="font-size: 1rem; color: var(--text-color-light); font-weight: 600;">Exit Code</div>
                </div>
                <div style="
                    text-align: center;
                    padding: 1.5rem;
                    background: linear-gradient(135deg, {status_bg}, rgba(255,255,255,0.02));
                    border-radius: 12px;
                    border: 1px solid {status_color}40;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
                ">
                    <div style="font-size: 2rem; font-weight: bold; color: {status_color}; margin-bottom: 0.5rem;">{status_text}</div>
                    <div style="font-size: 1rem; color: var(--text-color-light); font-weight: 600;">Status</div>
                </div>
                <div style="
                    text-align: center;
                    padding: 1.5rem;
                    background: linear-gradient(135deg, rgba(33, 150, 243, 0.08), rgba(33, 150, 243, 0.03));
                    border-radius: 12px;
                    border: 1px solid rgba(33, 150, 243, 0.2);
                    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
                ">
                    <div style="font-size: 2rem; font-weight: bold; color: #2196F3; margin-bottom: 0.5rem;">{formatted_time}</div>
                    <div style="font-size: 1rem; color: var(--text-color-light); font-weight: 600;">Completed</div>
                </div>
            </div>
            """, unsafe_allow_html=True)

        # Enhanced sections with professional styling
        _render_performance_metrics(test_results)
        _render_screenshots_section(test_results)
        _render_artifacts_section(test_results)
        _render_execution_output(test_results, success, verbose_mode)
        _render_success_summary(test_results, success, xml_results)

        debug("Execution results displayed successfully")

    except Exception as e:
        error_msg = f"Failed to display execution results: {e}"
        st.error(f"❌ **Display Error**: {error_msg}")
        debug(f"Error displaying execution results: {e}")


def _render_performance_metrics(test_results):
    """Render performance metrics section with enhanced professional styling and full-width layout."""
    performance_metrics = test_results.get('performance_metrics', {})
    if performance_metrics:
        st.markdown("""
        <div style="margin: 2rem 0 1rem 0;">
            <h3 style="color: var(--primary-color); margin-bottom: 0.5rem; font-size: 1.4rem; font-weight: 700;">
                ⚡ Performance Metrics
            </h3>
            <p style="color: var(--text-color-light); margin: 0; font-size: 1rem; opacity: 0.8;">
                Detailed performance analysis and system resource utilization
            </p>
        </div>
        """, unsafe_allow_html=True)

        # Create metrics using responsive HTML grid for full width
        metrics_data = performance_metrics.get('aggregated', {})
        if metrics_data:
            # Build HTML grid for metrics
            metric_items = [(name, data) for name, data in metrics_data.items()
                          if isinstance(data, dict) and 'average' in data]

            if metric_items:
                # Create responsive grid with enhanced styling
                grid_html = '''
                <div style="
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
                    gap: 1.5rem;
                    margin: 1.5rem 0;
                    width: 100%;
                ">'''

                for metric_name, metric_data in metric_items:
                    display_name = metric_name.replace('_', ' ').title()
                    average_val = metric_data['average']
                    min_val = metric_data.get('minimum', 'N/A')
                    max_val = metric_data.get('maximum', 'N/A')

                    # Smart display logic to avoid redundant min/max values
                    range_display = ""
                    if min_val != 'N/A' and max_val != 'N/A':
                        # Only show range if min and max are different and meaningful
                        if min_val != max_val and not (min_val == 0 and max_val == 0):
                            range_display = f"<div style=\"font-size: 0.8rem; color: var(--text-color-light); opacity: 0.7;\">Range: {min_val} - {max_val}</div>"
                        elif min_val == max_val and min_val != 0:
                            range_display = f"<div style=\"font-size: 0.8rem; color: var(--text-color-light); opacity: 0.7;\">Constant: {min_val}</div>"

                    grid_html += f"""
                    <div style="
                        text-align: center;
                        padding: 1.5rem;
                        background: linear-gradient(135deg, rgba(103, 58, 183, 0.08), rgba(103, 58, 183, 0.03));
                        border-radius: 12px;
                        border: 1px solid rgba(103, 58, 183, 0.2);
                        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
                        transition: transform 0.2s ease, box-shadow 0.2s ease;
                    ">
                        <div style="font-size: 1.8rem; font-weight: bold; color: var(--primary-color); margin-bottom: 0.5rem;">{average_val:.2f}</div>
                        <div style="font-size: 1rem; color: var(--text-color-light); font-weight: 600; margin-bottom: 0.3rem;">{display_name}</div>
                        {range_display}
                    </div>
                    """

                grid_html += '</div>'
                st.markdown(grid_html, unsafe_allow_html=True)


def _render_screenshots_section(test_results):
    """Render screenshots section with enhanced full-width layout and professional styling."""
    screenshots = test_results.get('screenshots', [])
    if screenshots:
        st.markdown("""
        <div style="margin: 2rem 0 1rem 0;">
            <h3 style="color: var(--primary-color); margin-bottom: 0.5rem; font-size: 1.4rem; font-weight: 700;">
                📸 Screenshots Captured
            </h3>
            <p style="color: var(--text-color-light); margin: 0; font-size: 1rem; opacity: 0.8;">
                Visual evidence of test execution steps and browser interactions
            </p>
        </div>
        """, unsafe_allow_html=True)

        # Display screenshots in a responsive grid layout for full width utilization
        # Show up to 3 screenshots with enhanced styling
        screenshot_count = min(len(screenshots), 3)

        if screenshot_count == 1:
            # Single screenshot - center it with optimal size
            screenshot_path = screenshots[0]
            if os.path.exists(screenshot_path):
                st.markdown("""
                <div style="display: flex; justify-content: center; margin: 1.5rem 0;">
                """, unsafe_allow_html=True)
                st.image(
                    screenshot_path,
                    caption=f"Screenshot: {os.path.basename(screenshot_path)}",
                    width=600  # Larger width for single screenshot
                )
                st.markdown("</div>", unsafe_allow_html=True)
        else:
            # Multiple screenshots - use responsive grid
            cols = st.columns(screenshot_count)
            for i, screenshot_path in enumerate(screenshots[:screenshot_count]):
                if os.path.exists(screenshot_path):
                    with cols[i]:
                        st.image(
                            screenshot_path,
                            caption=f"Screenshot {i+1}",
                            use_column_width=True  # Responsive width
                        )

        if len(screenshots) > 3:
            st.info(f"📷 Showing first 3 screenshots. {len(screenshots) - 3} more captured during execution.")


def _render_artifacts_section(test_results):
    """Render test artifacts section with enhanced professional styling and full-width layout."""
    artifacts = test_results.get('artifacts', {})
    if artifacts:
        st.markdown("""
        <div style="margin: 2rem 0 1rem 0;">
            <h3 style="color: var(--primary-color); margin-bottom: 0.5rem; font-size: 1.4rem; font-weight: 700;">
                📁 Test Artifacts
            </h3>
            <p style="color: var(--text-color-light); margin: 0; font-size: 1rem; opacity: 0.8;">
                Generated files and resources from test execution
            </p>
        </div>
        """, unsafe_allow_html=True)

        # Display artifacts in a structured format with enhanced styling
        for artifact_name, artifact_path in artifacts.items():
            if os.path.exists(artifact_path):
                # Extract file size for display
                try:
                    file_size = os.path.getsize(artifact_path)
                    if file_size < 1024:
                        size_display = f"{file_size} B"
                    elif file_size < 1024 * 1024:
                        size_display = f"{file_size / 1024:.1f} KB"
                    else:
                        size_display = f"{file_size / (1024 * 1024):.1f} MB"
                except:
                    size_display = "Unknown size"

                st.markdown(f"""
                <div style="
                    background: linear-gradient(135deg, rgba(103, 58, 183, 0.08), rgba(103, 58, 183, 0.03));
                    padding: 1.2rem;
                    border-radius: 10px;
                    margin: 1rem 0;
                    border-left: 4px solid var(--primary-color);
                    border: 1px solid rgba(103, 58, 183, 0.2);
                    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
                    width: 100%;
                ">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                        <strong style="color: var(--primary-color); font-size: 1.1rem;">{artifact_name}</strong>
                        <span style="color: var(--text-color-light); font-size: 0.9rem; opacity: 0.8;">{size_display}</span>
                    </div>
                    <code style="
                        background: rgba(0,0,0,0.1);
                        padding: 0.3rem 0.6rem;
                        border-radius: 4px;
                        font-size: 0.9rem;
                        word-break: break-all;
                    ">{artifact_path}</code>
                </div>
                """, unsafe_allow_html=True)


def _render_execution_output(test_results, success, verbose_mode):
    """Render execution output section with enhanced formatting and full-width layout."""
    if verbose_mode or not success:
        st.markdown("""
        <div style="margin: 2rem 0 1rem 0;">
            <h3 style="color: var(--primary-color); margin-bottom: 0.5rem; font-size: 1.4rem; font-weight: 700;">
                📝 Execution Output
            </h3>
            <p style="color: var(--text-color-light); margin: 0; font-size: 1rem; opacity: 0.8;">
                Detailed execution logs and debugging information
            </p>
        </div>
        """, unsafe_allow_html=True)

        # Create tabs for different output types with enhanced styling
        output_tabs = st.tabs(["📤 Standard Output", "⚠️ Standard Error", "❌ Failure Summary"])

        with output_tabs[0]:
            stdout = test_results.get('stdout', '')
            if stdout:
                # Enhanced code display with better formatting
                st.markdown("""
                <div style="margin: 1rem 0;">
                    <h4 style="color: var(--primary-color); font-size: 1.1rem; margin-bottom: 0.5rem;">Standard Output</h4>
                </div>
                """, unsafe_allow_html=True)
                st.code(stdout, language="text")
            else:
                st.info("📭 No standard output captured during execution")

        with output_tabs[1]:
            stderr = test_results.get('stderr', '')
            if stderr:
                st.markdown("""
                <div style="margin: 1rem 0;">
                    <h4 style="color: #F44336; font-size: 1.1rem; margin-bottom: 0.5rem;">Error Output</h4>
                </div>
                """, unsafe_allow_html=True)
                st.code(stderr, language="text")
            else:
                st.success("✅ No error output captured - clean execution")

        with output_tabs[2]:
            if not success:
                st.markdown("""
                <div style="margin: 1rem 0;">
                    <h4 style="color: #F44336; font-size: 1.1rem; margin-bottom: 0.5rem;">Failure Analysis</h4>
                </div>
                """, unsafe_allow_html=True)

                stdout = test_results.get('stdout', '')
                if stdout and ("FAILED" in stdout or "ERROR" in stdout):
                    # Extract and show failure lines with enhanced formatting
                    stdout_lines = stdout.split('\n')
                    failure_lines = [line for line in stdout_lines if
                                   "FAILED" in line or "ERROR" in line or
                                   "AssertionError" in line or "TimeoutException" in line]
                    if failure_lines:
                        st.code('\n'.join(failure_lines[:10]), language="text")
                        if len(failure_lines) > 10:
                            st.caption(f"📋 Showing first 10 failure lines. {len(failure_lines) - 10} more available in Standard Output tab.")
                    else:
                        st.info("🔍 No specific failure details found in output")
                else:
                    st.info("📄 No failure details available in execution output")
            else:
                st.success("🎉 No failures to display - execution was successful!")


def _render_success_summary(test_results, success, xml_results):
    """Render success summary with enhanced celebration styling and full-width layout."""
    if success:
        st.markdown("""
        <div style="
            background: linear-gradient(135deg, rgba(76, 175, 80, 0.12), rgba(76, 175, 80, 0.06));
            border: 2px solid rgba(76, 175, 80, 0.4);
            border-radius: 15px;
            padding: 2rem;
            margin: 2rem 0;
            text-align: center;
            width: 100%;
            box-shadow: 0 4px 12px rgba(76, 175, 80, 0.2);
        ">
            <div style="margin-bottom: 1rem;">
                <span style="font-size: 3rem;">🎉</span>
            </div>
            <h3 style="
                color: #4CAF50;
                margin: 0 0 1rem 0;
                font-size: 1.8rem;
                font-weight: 700;
            ">
                Script Execution Successful!
            </h3>
            <p style="
                margin: 0;
                color: var(--text-color-light);
                font-size: 1.2rem;
                line-height: 1.5;
                max-width: 600px;
                margin: 0 auto;
            ">
                The generated automation script executed successfully with all tests passing.
                Your template-based script generation is working perfectly!
            </p>
        </div>
        """, unsafe_allow_html=True)

        # Show enhanced execution summary with full-width styling
        if xml_results and "summary" in xml_results:
            duration = xml_results["summary"].get("duration", 0)
            tests_run = xml_results["summary"].get("total_tests", 0)
            passed_tests = xml_results["summary"].get("passed_tests", 0)

            st.markdown(f"""
            <div style="
                background: linear-gradient(135deg, rgba(33, 150, 243, 0.08), rgba(33, 150, 243, 0.03));
                border: 1px solid rgba(33, 150, 243, 0.2);
                border-radius: 10px;
                padding: 1.5rem;
                margin: 1.5rem 0;
                text-align: center;
                width: 100%;
            ">
                <h4 style="color: #2196F3; margin: 0 0 1rem 0; font-size: 1.2rem;">
                    📊 Execution Summary
                </h4>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 1rem;">
                    <div>
                        <div style="font-size: 1.5rem; font-weight: bold; color: #4CAF50;">{tests_run}</div>
                        <div style="font-size: 0.9rem; color: var(--text-color-light);">Tests Executed</div>
                    </div>
                    <div>
                        <div style="font-size: 1.5rem; font-weight: bold; color: #4CAF50;">{passed_tests}</div>
                        <div style="font-size: 0.9rem; color: var(--text-color-light);">Tests Passed</div>
                    </div>
                    <div>
                        <div style="font-size: 1.5rem; font-weight: bold; color: #2196F3;">{duration:.2f}s</div>
                        <div style="font-size: 0.9rem; color: var(--text-color-light);">Total Duration</div>
                    </div>
                </div>
            </div>
            """, unsafe_allow_html=True)
